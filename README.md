# Tableyze
### *Revolutionizing how AI makes the most of tabular data*





## Project Structure

```
tableyze/
├── backend/                 # Django REST API
│   ├── tableyze_backend/   # Main Django project
│   ├── venv/               # Python virtual environment
│   ├── requirements.txt    # Python dependencies
│   ├── manage.py          # Django management script
│   └── .env.example       # Environment variables template
├── frontend/              # React application
│   ├── src/              # React source code
│   ├── public/           # Static assets
│   └── package.json      # Node.js dependencies
├── package.json          # Root package.json for project scripts
├── .gitignore           # Git ignore rules
└── README.md            # This file
```

## Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn

### Setup
1. Clone the repository
2. Install dependencies:
   ```bash
   npm run setup
   ```

### Development
Run both backend and frontend in development mode:
```bash
npm run dev
```

Or run them separately:
```bash
# Backend only (Django on http://localhost:8000)
npm run backend:dev

# Frontend only (React on http://localhost:3000)
npm run frontend:dev
```

### Environment Configuration
1. Copy the environment template:
   ```bash
   cp backend/.env.example backend/.env
   ```
2. Update the `.env` file with your configuration

### Available Scripts
- `npm run dev` - Run both backend and frontend
- `npm run backend:dev` - Run Django development server
- `npm run frontend:dev` - Run React development server
- `npm run backend:migrate` - Run Django migrations
- `npm run backend:makemigrations` - Create new Django migrations
- `npm run backend:createsuperuser` - Create Django admin user
- `npm run frontend:build` - Build React for production

## Features

### Backend (Django REST Framework)
- RESTful API architecture
- SQLite database (development)
- AWS S3 integration for file storage
- CORS enabled for frontend communication
- Environment-based configuration
- Admin interface

### Frontend (React)
- Modern React application
- Ready for API integration
- Development server with hot reload

## AWS S3 Configuration
The application is configured to use AWS S3 for file storage in production. Set `USE_S3=True` in your environment variables and provide the necessary AWS credentials.

