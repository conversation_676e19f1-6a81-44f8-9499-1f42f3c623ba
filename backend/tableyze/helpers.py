import pandas as pd
from minepy import MINE
from sklearn.preprocessing import <PERSON><PERSON>ncoder


def compute_mic(df: pd.DataFrame) -> dict[tuple[str, str], float]:
    """
    Compute the Maximal Information Coefficient (MIC) for all pairs of columns 
    in a mixed-type DataFrame (numeric + categorical).
    
    Parameters
    ----------
    df : pd.DataFrame
        Input dataset with mixed types.
    
    Returns
    -------
    dict[tuple[str, str], float]
        Dictionary of MIC values where both (col1, col2) and (col2, col1) exist.
    """
    # Encode categorical variables as integers
    encoded_df = df.copy()
    for col in encoded_df.columns:
        if encoded_df[col].dtype == "object" or str(encoded_df[col].dtype).startswith("category"):
            encoded_df[col] = LabelEncoder().fit_transform(encoded_df[col].astype(str))
    
    cols = encoded_df.columns
    n = len(cols)
    mic_dict: dict[tuple[str, str], float] = {}
    
    mine = MINE(alpha=0.6, c=15)  # Default parameters from <PERSON><PERSON><PERSON> et al.
    
    for i in range(n):
        for j in range(i, n):
            x = encoded_df.iloc[:, i].values
            y = encoded_df.iloc[:, j].values
            
            mine.compute_score(x, y)
            mic = mine.mic()
            
            col1, col2 = cols[i], cols[j]
            mic_dict[(col1, col2)] = mic
            mic_dict[(col2, col1)] = mic  # Explicit symmetric entry
    
    return mic_dict


def get_top_mic_pairs(mic_pairs: dict, top_n: int = 5) -> list[dict]:
    """
    Compute the MIC dictionary for all features (numeric + categorical) 
    and return the top correlated feature pairs.

    Parameters
    ----------
    df : pd.DataFrame
        Input dataset with mixed feature types.
    top_n : int, optional
        Number of top pairs to return (default = 5).

    Returns
    -------
    list[dict]
        List of top correlated feature pairs with MIC scores.
    """

    corr_pairs = []
    seen = set()
    for (col1, col2), mic_value in mic_pairs.items():
        if col1 == col2 or (col2, col1) in seen:
            continue
        seen.add((col1, col2))
        corr_pairs.append({
            "feature1": col1,
            "feature2": col2,
            "correlation": float(mic_value),
            "correlation_raw": float(mic_value)
        })
    
    # Sort by MIC and take top N
    corr_pairs.sort(key=lambda x: x["correlation"], reverse=True)
    return corr_pairs[:top_n]
