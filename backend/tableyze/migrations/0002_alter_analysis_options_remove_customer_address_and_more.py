# Generated by Django 4.2.7 on 2025-09-01 19:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tableyze', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='analysis',
            options={'ordering': ['-last_edited_at', '-last_seen_at'], 'verbose_name': 'Analysis', 'verbose_name_plural': 'Analyses'},
        ),
        migrations.RemoveField(
            model_name='customer',
            name='address',
        ),
        migrations.RemoveField(
            model_name='customer',
            name='phone_number',
        ),
        migrations.RemoveField(
            model_name='customer',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='dataset',
            name='file_size',
        ),
        migrations.AddField(
            model_name='analysis',
            name='last_seen_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='customer',
            name='pricing_plan',
            field=models.IntegerField(choices=[(1, 'Free Test Unlimited'), (2, 'Free Tier'), (3, 'Paid Plan')], default=1, verbose_name='Preistarif'),
        ),
    ]
