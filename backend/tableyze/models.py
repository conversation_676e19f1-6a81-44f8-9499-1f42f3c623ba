from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from .choices import PRICING_PLAN_CHOICES


class Customer(models.Model):
    """Customer model for user profile information"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='customer_profile')
    company_name = models.CharField(max_length=255, blank=True, null=True)
    pricing_plan = models.IntegerField("Preistarif", choices=PRICING_PLAN_CHOICES, default=1)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.company_name or 'No Company'}"

    class Meta:
        verbose_name = "Customer"
        verbose_name_plural = "Customers"


class Analysis(models.Model):
    """Analysis model for storing user analysis sessions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='analyses')
    name = models.<PERSON>r<PERSON><PERSON>(max_length=255)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_edited_at = models.DateTimeField(auto_now=True)
    last_seen_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} - {self.user.username}"

    class Meta:
        verbose_name = "Analysis"
        verbose_name_plural = "Analyses"
        ordering = ['-last_edited_at', '-last_seen_at']


class Dataset(models.Model):
    """Dataset model for storing dataset information and status"""

    STATUS_CHOICES = [
        ('raw', 'Raw'),
        ('preprocessed', 'Preprocessed'),
        ('embedded', 'Embedded'),
        ('predicted', 'Predicted'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='datasets')
    analysis = models.ForeignKey(Analysis, on_delete=models.CASCADE, related_name='datasets')
    name = models.CharField(max_length=255)
    file_url = models.URLField(max_length=500, help_text="Local URL or S3 URI")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='raw')
    rows_count = models.IntegerField(null=True, blank=True)
    columns_count = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.status}) - {self.analysis.name}"

    class Meta:
        verbose_name = "Dataset"
        verbose_name_plural = "Datasets"
        ordering = ['-created_at']
