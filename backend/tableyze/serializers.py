from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Customer, Analysis, Dataset


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']


class CustomerSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = Customer
        fields = ['id', 'user', 'company_name', 'phone_number', 'address', 'created_at', 'updated_at']


class AnalysisSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    datasets_count = serializers.SerializerMethodField()

    class Meta:
        model = Analysis
        fields = ['id', 'name', 'description', 'created_at', 'last_edited_at', 'is_active', 'user', 'datasets_count']
        read_only_fields = ['created_at', 'last_edited_at', 'user']

    def get_datasets_count(self, obj):
        return obj.datasets.count()


class DatasetSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    analysis_name = serializers.CharField(source='analysis.name', read_only=True)

    class Meta:
        model = Dataset
        fields = ['id', 'name', 'file_url', 'status', 'rows_count', 'columns_count',
                 'created_at', 'updated_at', 'user', 'analysis', 'analysis_name']
        read_only_fields = ['created_at', 'updated_at', 'user']


class AnalysisDetailSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    datasets = DatasetSerializer(many=True, read_only=True)

    class Meta:
        model = Analysis
        fields = ['id', 'name', 'description', 'created_at', 'last_edited_at', 'is_active', 'user', 'datasets']
        read_only_fields = ['created_at', 'last_edited_at', 'user']