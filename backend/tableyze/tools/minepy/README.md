# Minimal MINE Implementation

This directory contains a minimal implementation of the MINE (Maximal Information-based Nonparametric Exploration) algorithm, specifically focused on calculating the **Maximal Information Coefficient (MIC)**.

## What's Included

This minimal version includes only the essential components needed for MIC calculation:

- **Core C library** (`libmine/`): The fundamental MINE algorithm implementation
- **Python wrapper** (`minepy/`): Cython-based Python interface to the C library
- **Setup script**: For building the extension module
- **Example usage**: Demonstrates how to calculate MIC scores

## Installation

### Prerequisites
- Python 3.x
- NumPy >= 1.3.0
- C compiler (gcc, clang, or MSVC on Windows)
- Cython (for building from source)

### Build the Extension

1. Install dependencies:
   ```bash
   pip install numpy cython
   ```

2. Build the extension module:
   ```bash
   python setup.py build_ext --inplace
   ```

3. (Optional) Install the package:
   ```bash
   pip install -e .
   ```

## Usage

### Basic MIC Calculation

```python
import numpy as np
from minepy import MINE

# Create sample data
x = np.linspace(0, 1, 100)
y = np.sin(10 * np.pi * x) + x

# Initialize MINE
mine = MINE(alpha=0.6, c=15, est="mic_approx")

# Compute score
mine.compute_score(x, y)

# Get MIC value
mic_score = mine.mic()
print(f"MIC score: {mic_score}")
```

### Parameters

- **alpha** (float): Controls the search-grid size. Default: 0.6
  - If alpha ∈ (0,1]: B = max(n^alpha, 4) where n is sample size
  - If alpha ≥ 4: B = min(alpha, n)
  
- **c** (float): Determines clump number in partitions. Default: 15
  - Higher values = more detailed search but slower computation
  
- **est** (str): Estimator type. Options:
  - "mic_approx": Original MIC estimator (default)
  - "mic_e": MIC_e estimator

### Available Methods

After calling `compute_score(x, y)`, you can access:

- `mine.mic()`: Maximal Information Coefficient
- `mine.mas()`: Maximum Asymmetry Score  
- `mine.mev()`: Maximum Edge Value
- `mine.mcn(eps)`: Minimum Cell Number
- `mine.mcn_general()`: MCN with eps = 1 - MIC
- `mine.tic()`: Total Information Coefficient
- `mine.gmic(p)`: Generalized MIC

## Example Script

Run the included example:

```bash
python example_usage.py
```

This demonstrates MIC calculation for various relationship types:
- Sinusoidal relationships
- Linear relationships  
- Noisy relationships
- Independent variables

## MIC Score Interpretation

- **MIC = 0**: No relationship detected
- **MIC = 1**: Perfect functional relationship
- **0 < MIC < 1**: Strength of relationship

MIC is designed to detect both linear and non-linear relationships and is symmetric (MIC(X,Y) = MIC(Y,X)).

## Files Structure

```
minepy_minimal/
├── libmine/           # Core C implementation
│   ├── mine.c
│   └── mine.h
├── minepy/            # Python wrapper
│   ├── __init__.py
│   ├── mine.pyx       # Cython source
│   └── libmine.pxd    # Cython declarations
├── setup.py           # Build configuration
├── requirements.txt   # Dependencies
├── example_usage.py   # Usage examples
└── README.md         # This file
```

## License

This code is derived from the original minepy library and maintains the same GPL-3.0 license.
