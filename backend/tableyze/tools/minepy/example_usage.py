#!/usr/bin/env python3
"""
Example usage of the minimal MINE implementation for MIC calculation.
This demonstrates how to use the MINE class to compute MIC scores.
"""

import numpy as np
from minepy import MINE

def simple_mic_example():
    """Simple example showing MIC calculation between two variables."""
    print("=== Simple MIC Example ===")
    
    # Create some sample data
    x = np.linspace(0, 1, 100)
    y = np.sin(10 * np.pi * x) + x  # Sinusoidal relationship
    
    # Initialize MINE with default parameters
    mine = MINE(alpha=0.6, c=15, est="mic_approx")
    
    # Compute the score
    mine.compute_score(x, y)
    
    # Get the MIC value
    mic_score = mine.mic()
    
    print(f"MIC score between x and y: {mic_score:.4f}")
    return mic_score

def linear_relationship_example():
    """Example with a perfect linear relationship."""
    print("\n=== Linear Relationship Example ===")
    
    x = np.linspace(0, 10, 50)
    y = 2 * x + 3  # Perfect linear relationship
    
    mine = MINE(alpha=0.6, c=15)
    mine.compute_score(x, y)
    mic_score = mine.mic()
    
    print(f"MIC score for perfect linear relationship: {mic_score:.4f}")
    return mic_score

def noisy_relationship_example():
    """Example with noise added to the relationship."""
    print("\n=== Noisy Relationship Example ===")
    
    np.random.seed(42)  # For reproducible results
    x = np.linspace(0, 1, 100)
    y = np.sin(10 * np.pi * x) + x
    y += np.random.normal(0, 0.1, len(x))  # Add noise
    
    mine = MINE(alpha=0.6, c=15)
    mine.compute_score(x, y)
    mic_score = mine.mic()
    
    print(f"MIC score with noise: {mic_score:.4f}")
    return mic_score

def independence_example():
    """Example with independent variables."""
    print("\n=== Independence Example ===")
    
    np.random.seed(42)
    x = np.random.normal(0, 1, 100)
    y = np.random.normal(0, 1, 100)  # Independent random variables
    
    mine = MINE(alpha=0.6, c=15)
    mine.compute_score(x, y)
    mic_score = mine.mic()
    
    print(f"MIC score for independent variables: {mic_score:.4f}")
    return mic_score

if __name__ == "__main__":
    print("Minimal MINE Implementation - MIC Calculation Examples")
    print("=" * 55)
    
    try:
        simple_mic_example()
        linear_relationship_example()
        noisy_relationship_example()
        independence_example()
        
        print("\n=== Summary ===")
        print("All examples completed successfully!")
        print("MIC values range from 0 (no relationship) to 1 (perfect relationship)")
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("You may need to build the extension first:")
        print("  python setup.py build_ext --inplace")
    except Exception as e:
        print(f"Error: {e}")
