from setuptools import setup, Extension
from setuptools.command.build_ext import build_ext
import platform

# import numpy only when it is needed
class build_ext_custom(build_ext):
    def run(self):
        import numpy
        self.include_dirs.append(numpy.get_include())
        build_ext.run(self)

if platform.system() == "Windows":
    libraries = []
else:
    libraries = ['m']

ext_modules = [
    Extension("minepy.mine",
              ["minepy/mine.c", "libmine/mine.c"],
              libraries=libraries,
              extra_compile_args=['-Wall'])
    ]

setup(name = 'minepy_minimal',
      version='1.2.6',
      description='Minimal MINE implementation for MIC calculation',
      author='Extracted from minepy',
      packages=['minepy'],
      setup_requires = ['numpy >= 1.3.0'],
      install_requires = ['numpy >= 1.3.0'],
      ext_modules=ext_modules,
      cmdclass = {'build_ext': build_ext_custom}
    )
