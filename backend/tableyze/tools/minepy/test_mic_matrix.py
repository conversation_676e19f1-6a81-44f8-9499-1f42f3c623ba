#!/usr/bin/env python3
"""
Test script that replicates the original MIC matrix calculation functionality
using the minimal MINE implementation.
"""

import pandas as pd
import numpy as np
from minepy import MINE
from sklearn.preprocessing import LabelEncoder


def decorator_timer(some_function):
    from time import time

    def wrapper(*args, **kwargs):
        t1 = time()
        result = some_function(*args, **kwargs)
        end = time()-t1
        return result, end
    return wrapper


@decorator_timer
def compute_mic_matrix(df: pd.DataFrame) -> pd.DataFrame:
    """
    Compute the Maximal Information Coefficient (MIC) for all pairs of columns 
    in a mixed-type DataFrame (numeric + categorical).
    
    Parameters
    ----------
    df : pd.DataFrame
        Input dataset with mixed types.
    
    Returns
    -------
    pd.DataFrame
        A symmetric MIC matrix with values in [0, 1].
    """
    # Encode categorical variables as integers
    encoded_df = df.copy()
    for col in encoded_df.columns:
        if encoded_df[col].dtype == "object" or str(encoded_df[col].dtype).startswith("category"):
            encoded_df[col] = LabelEncoder().fit_transform(encoded_df[col].astype(str))
    
    cols = encoded_df.columns
    n = len(cols)
    mic_matrix = pd.DataFrame(0.0, index=cols, columns=cols)
    
    mine = MINE(alpha=0.6, c=15)  # Default parameters from Reshef et al.
    
    for i in range(n):
        for j in range(i, n):
            x = encoded_df.iloc[:, i].values
            y = encoded_df.iloc[:, j].values
            
            mine.compute_score(x, y)
            mic = mine.mic()
            
            mic_matrix.iloc[i, j] = mic
            mic_matrix.iloc[j, i] = mic  # Symmetric
    
    return mic_matrix


def create_sample_data():
    """Create a sample dataset for testing."""
    np.random.seed(42)
    n_samples = 100
    
    # Create mixed-type data
    data = {
        'numeric1': np.random.normal(0, 1, n_samples),
        'numeric2': np.random.normal(0, 1, n_samples),
        'categorical1': np.random.choice(['A', 'B', 'C'], n_samples),
        'categorical2': np.random.choice(['X', 'Y'], n_samples),
    }
    
    # Add some relationships
    data['numeric_related'] = data['numeric1'] * 2 + np.random.normal(0, 0.1, n_samples)
    data['cat_related'] = ['High' if x > 0 else 'Low' for x in data['numeric1']]
    
    return pd.DataFrame(data)


if __name__ == "__main__":
    print("Testing MIC Matrix Calculation with Minimal MINE Implementation")
    print("=" * 65)
    
    # Create sample data
    print("Creating sample dataset...")
    df = create_sample_data()
    print(f"Dataset shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"Data types:\n{df.dtypes}")
    
    print("\nFirst few rows:")
    print(df.head())
    
    # Compute MIC matrix
    print("\nComputing MIC matrix...")
    mic_matrix, computation_time = compute_mic_matrix(df)
    
    print(f"Computation completed in {computation_time:.2f} seconds")
    print("\nMIC Matrix:")
    print(mic_matrix.round(4))
    
    # Analyze results
    print("\n=== Analysis ===")
    print("Expected high correlations:")
    print(f"numeric1 vs numeric_related: {mic_matrix.loc['numeric1', 'numeric_related']:.4f}")
    print(f"numeric1 vs cat_related: {mic_matrix.loc['numeric1', 'cat_related']:.4f}")
    
    print("\nExpected low correlations:")
    print(f"numeric1 vs numeric2: {mic_matrix.loc['numeric1', 'numeric2']:.4f}")
    print(f"categorical1 vs categorical2: {mic_matrix.loc['categorical1', 'categorical2']:.4f}")
    
    print("\nDiagonal (self-correlation) should be 1.0:")
    diagonal_values = [mic_matrix.iloc[i, i] for i in range(len(mic_matrix))]
    print(f"Diagonal values: {[f'{v:.4f}' for v in diagonal_values]}")
    
    print("\n✓ Test completed successfully!")
