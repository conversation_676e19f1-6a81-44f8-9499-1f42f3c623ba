from django.urls import path
from . import views

app_name = 'tableyze'

urlpatterns = [
    # Dashboard and overview
    path('dashboard/', views.dashboard_overview, name='dashboard'),

    # Analysis endpoints
    path('analyses/', views.AnalysisListCreateView.as_view(), name='analysis-list'),
    path('analyses/<int:pk>/', views.AnalysisDetailView.as_view(), name='analysis-detail'),

    # Dataset endpoints
    path('analyses/<int:analysis_id>/datasets/', views.DatasetListCreateView.as_view(), name='dataset-list'),
    path('datasets/<int:pk>/', views.DatasetDetailView.as_view(), name='dataset-detail'),
    path('datasets/<int:pk>/preview/', views.dataset_preview, name='dataset-preview'),
    path('datasets/<int:pk>/drop-columns/', views.dataset_drop_columns, name='dataset-drop-columns'),
    path('datasets/<int:pk>/dataset-user-context/', views.dataset_user_context, name='dataset-user-context'),
    path('datasets/<int:pk>/analyze/', views.dataset_analyze, name='dataset-analyze'),
    path('datasets/<int:pk>/target-selection/', views.dataset_target_selection, name='dataset-target-selection'),
    path('datasets/<int:pk>/target-selection-persist/', views.dataset_target_selection_persist, name='dataset-target-selection-persist'),
]