import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import StartPage from './pages/StartPage';
import AnalysisPage from './pages/AnalysisPage';
import './styles/global.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<StartPage />} />
          <Route path="/analysis/:id" element={<AnalysisPage />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
