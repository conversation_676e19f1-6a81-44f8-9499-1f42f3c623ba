.analysis-card {
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.analysis-card__content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.analysis-card__content--new {
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 180px;
}

.analysis-card--new {
  background-color: var(--color-gray-50);
  border: 2px dashed var(--color-border);
  color: var(--color-text-secondary);
}

.analysis-card--new:hover {
  background-color: var(--color-gray-100);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.analysis-card__icon {
  margin-bottom: var(--spacing-md);
  opacity: 0.7;
}

.analysis-card__header {
  margin-bottom: var(--spacing-md);
}

.analysis-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-tight);
}

.analysis-card--new .analysis-card__title {
  color: inherit;
  margin-bottom: var(--spacing-xs);
}

.analysis-card__subtitle {
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
  margin: 0;
}

.analysis-card__meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.analysis-card__date {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.analysis-card__datasets {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  background-color: var(--color-gray-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
}

.analysis-card__description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-md);
  flex-grow: 1;

  /* Limit to 3 lines */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.analysis-card__footer {
  margin-top: auto;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border-light);
}

.analysis-card__created {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
}

/* Hover effects */
.analysis-card:hover .analysis-card__title {
  color: var(--color-primary);
}

.analysis-card--new:hover .analysis-card__title {
  color: inherit;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .analysis-card {
    min-height: 160px;
  }

  .analysis-card__content--new {
    min-height: 140px;
  }

  .analysis-card__meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}