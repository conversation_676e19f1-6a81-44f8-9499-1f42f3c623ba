import React from 'react';
import { useNavigate } from 'react-router-dom';
import './AnalysisCard.css';

const AnalysisCard = ({ analysis, isNewCard = false }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (isNewCard) {
      navigate('/analysis/new');
    } else {
      navigate(`/analysis/${analysis.id}`);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (isNewCard) {
    return (
      <div className="analysis-card analysis-card--new card card-clickable" onClick={handleClick}>
        <div className="analysis-card__content analysis-card__content--new">
          <div className="analysis-card__icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </div>
          <h3 className="analysis-card__title">New Analysis</h3>
          <p className="analysis-card__subtitle">Start a new data analysis</p>
        </div>
      </div>
    );
  }

  return (
    <div className="analysis-card card card-clickable" onClick={handleClick}>
      <div className="analysis-card__content">
        <div className="analysis-card__header">
          <h3 className="analysis-card__title">{analysis.name}</h3>
          <div className="analysis-card__meta">
            <span className="analysis-card__date">
              {formatDate(analysis.last_edited_at)}
            </span>
            {analysis.datasets_count > 0 && (
              <span className="analysis-card__datasets">
                {analysis.datasets_count} dataset{analysis.datasets_count !== 1 ? 's' : ''}
              </span>
            )}
          </div>
        </div>

        {analysis.description && (
          <p className="analysis-card__description">{analysis.description}</p>
        )}

        <div className="analysis-card__footer">
          <span className="analysis-card__created">
            Created {formatDate(analysis.created_at)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default AnalysisCard;