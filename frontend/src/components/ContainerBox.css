.container-box {
  display: flex;
  flex-direction: column;
  position: relative; /* allow positioning the ready button on the edge */
  margin-bottom: var(--spacing-3xl);
}

.container-box__title {
  margin: 0 0 var(--spacing-md) 0;
}

.container-box__content {
  padding-bottom: var(--spacing-xl);
}

.container-box__footer {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0; /* align with the bottom border */
  height: 0; /* no extra height taken */
  display: flex;
  align-items: center; /* center vertically */
  justify-content: center;
  pointer-events: none; /* allow underlying content interactions except on the button */
  transform: translateY(50%); /* center the button on the border */
}

.container-box__ready {
  pointer-events: auto; /* enable interaction */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xxs);
  background: var(--color-surface);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.container-box__ready:hover {
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-dark);
  box-shadow: var(--shadow-md);
}

.container-box__ready-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.container-box__ready-icon svg {
  transition: transform var(--transition-fast);
}

.container-box__ready:hover .container-box__ready-icon svg {
  transform: translateY(1px);
}

.container-box__section-title__h3 {
  margin-bottom: var(--spacing-md);
}
