import React, { useRef } from 'react';
import './ContainerBox.css';

const ContainerBox = ({ title, children, isReady = false, readyLabel = 'Continue', onReadyClick, scrollToNext = true }) => {
  const containerRef = useRef(null);

  const handleReadyClick = () => {
    if (onReadyClick) {
      onReadyClick();
    }
    
    if (scrollToNext && containerRef.current) {
      // Wait for next section to render, then scroll
      setTimeout(() => {
        const nextElement = containerRef.current.nextElementSibling;
        if (nextElement) {
          nextElement.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
          });
        }
      }, 100);
    }
  };

  return (
    <section ref={containerRef} className="container-box card">
      {title && <h2 className="container-box__title">{title}</h2>}
      <div className="container-box__content">
        {children}
      </div>
      <div className="container-box__footer">
        {isReady && (
          <button 
            type='button'
            className="container-box__ready"
            onClick={handleReadyClick}
            aria-label={readyLabel}
          >
            {readyLabel}
            <span className="container-box__ready-icon" aria-hidden>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-chevron-compact-down" viewBox="0 0 16 16">
                <path fillRule="evenodd" d="M1.553 6.776a.5.5 0 0 1 .67-.223L8 9.44l5.776-2.888a.5.5 0 1 1 .448.894l-6 3a.5.5 0 0 1-.448 0l-6-3a.5.5 0 0 1-.223-.67"/>
              </svg>
            </span>
          </button>
        )}
      </div>
    </section>
  );
};

export default ContainerBox;

