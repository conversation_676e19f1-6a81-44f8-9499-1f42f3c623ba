import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';
import ContainerBox from './ContainerBox';
import './DataAnalysisContainer.css';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const DataAnalysisContainer = ({ analysis, visible, onAnalysisComplete }) => {
  const [analysisData, setAnalysisData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const isReady = analysisData && !loading;

  useEffect(() => {
    if (visible && analysis?.datasets?.[0]?.id && !analysisData) {
      performAnalysis();
    }
  }, [visible, analysis?.datasets, analysisData]);

  const performAnalysis = async () => {
    if (!analysis?.datasets?.[0]?.id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const { apiService } = await import('../services/api');
      const response = await apiService.analyzeDataset(analysis.datasets[0].id);
      setAnalysisData(response.data);
      onAnalysisComplete?.(response.data);
    } catch (err) {
      setError('Failed to analyze dataset. Please try again.');
      console.error('Analysis error:', err);
    } finally {
      setLoading(false);
    }
  };

  const createHistogramData = (feature) => {
    if (feature.type === 'numerical') {
      const { counts, bin_edges } = feature.histogram;
      const labels = bin_edges.slice(0, -1).map((edge, i) => 
        `${edge.toFixed(2)}-${bin_edges[i + 1].toFixed(2)}`
      );
      
      return {
        labels,
        datasets: [{
          data: counts,
          backgroundColor: 'rgba(59, 130, 246, 0.6)',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 1,
        }]
      };
    } else if (feature.type === 'categorical') {
      const { categories, counts } = feature.histogram;
      return {
        labels: categories,
        datasets: [{
          data: counts,
          backgroundColor: 'rgba(139, 92, 246, 0.6)',
          borderColor: 'rgba(139, 92, 246, 1)',
          borderWidth: 1,
        }]
      };
    }
    return null;
  };

  const createCorrelationGaugeData = (correlation) => {
    const absCorr = Math.abs(correlation.correlation_raw);
    const remaining = 1 - absCorr;
    
    // Color based on correlation strength: red (low) to green (high)
    const red = Math.round(255 * (1 - absCorr));
    const green = Math.round(200 * absCorr); // Reduced from 255 to 200 for less bright green
    const color = `rgb(${red}, ${green}, 0)`;
    
    return {
      labels: ['Correlation', 'Remaining'],
      datasets: [{
        data: [absCorr, remaining],
        backgroundColor: [color, 'rgba(229, 231, 235, 0.3)'],
        borderWidth: 0,
        cutout: '70%',
      }]
    };
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: { enabled: true }
    },
    scales: {
      x: { display: false },
      y: { display: false }
    }
  };

  const gaugeOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: { enabled: false }
    }
  };

  if (!visible) return null;

  return (
    <ContainerBox
      title="2. Data Analysis"
      isReady={isReady}
      readyLabel="Target Selection"
      onReadyClick={() => {/* parent can handle next container */}}
    >
      {loading && (
        <div className="analysis-loading">
          <div className="loading-spinner"></div>
          <p>Analyzing your data...</p>
        </div>
      )}

      {error && (
        <div className="analysis-error">
          <p>{error}</p>
          <button className="btn btn-primary btn-sm" onClick={performAnalysis}>
            Retry Analysis
          </button>
        </div>
      )}

      {analysisData && (
        <div className="analysis-results">
          {/* Features Section */}
          <div className="analysis-section">
            <h3 className="analysis-section__title">Features</h3>
            <div className="features-scroll">
              {analysisData.features.map((feature, index) => {
                const histogramData = createHistogramData(feature);
                return (
                  <div key={index} className="feature-card">
                    <div className="feature-card__header">
                      <span className={`feature-type feature-type--${feature.type}`}>
                        {feature.type}
                      </span>
                      <h4 className="feature-card__name">{feature.name}</h4>
                    </div>
                    
                    <div className="feature-card__chart">
                      {histogramData && (
                        <Bar data={histogramData} options={chartOptions} />
                      )}
                    </div>
                    
                    <div className="feature-card__stats">
                      {feature.type === 'numerical' && (
                        <>
                          <div className="stat-item">
                            <span className="stat-label">Mean:</span>
                            <span className="stat-value">{feature.stats.mean?.toFixed(2)}</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-label">Median:</span>
                            <span className="stat-value">{feature.stats.median?.toFixed(2)}</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-label">Min:</span>
                            <span className="stat-value">{feature.stats.min?.toFixed(2)}</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-label">Max:</span>
                            <span className="stat-value">{feature.stats.max?.toFixed(2)}</span>
                          </div>
                        </>
                      )}
                      
                      {feature.type === 'categorical' && (
                        <>
                          <div className="stat-item">
                            <span className="stat-label">Most Common:</span>
                            <span className="stat-value">{feature.stats.most_common}</span>
                          </div>
                          <div className="stat-item">
                            <span className="stat-label">Unique Values:</span>
                            <span className="stat-value">{feature.stats.unique_count}</span>
                          </div>
                        </>
                      )}
                      
                      <div className="stat-item">
                        <span className="stat-label">Count:</span>
                        <span className="stat-value">{feature.stats.count}</span>
                      </div>
                      
                      {feature.stats.null_count > 0 && (
                        <div className="stat-item">
                          <span className="stat-label">Missing:</span>
                          <span className="stat-value">{feature.stats.null_count}</span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Correlations Section */}
          {analysisData.correlations.length > 0 && (
            <div className="analysis-section">
              <h3 className="analysis-section__title">Correlations</h3>
              <div className="correlations-grid">
                {analysisData.correlations.map((corr, index) => {
                  const gaugeData = createCorrelationGaugeData(corr);
                  return (
                    <div key={index} className="correlation-card">
                      <div className="correlation-card__chart">
                        <Doughnut data={gaugeData} options={gaugeOptions} />
                        <div className="correlation-card__value">
                          {Math.abs(corr.correlation_raw).toFixed(2)}
                        </div>
                      </div>
                      <div className="correlation-card__labels">
                        <div className="correlation-card__feature">{corr.feature1}</div>
                        <div className="correlation-card__vs">vs</div>
                        <div className="correlation-card__feature">{corr.feature2}</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Summary */}
          <div className="analysis-summary">
            <p>
              Analyzed {analysisData.total_rows} rows with {analysisData.remaining_columns} features
              {analysisData.dropped_columns.user_selected.length + analysisData.dropped_columns.auto_detected.length > 0 && (
                <span> (dropped {analysisData.dropped_columns.user_selected.length + analysisData.dropped_columns.auto_detected.length} columns)</span>
              )}
            </p>
          </div>
        </div>
      )}
    </ContainerBox>
  );
};

export default DataAnalysisContainer;
