.upload-dropzone {
  width: 100%;
  border: 2px dashed var(--color-border);
  background: var(--color-gray-50);
  color: var(--color-text-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  transition: background var(--transition-fast), border-color var(--transition-fast);
}
.upload-dropzone:hover {
  border-color: var(--color-border-dark);
}

.upload-dropzone { cursor: pointer; }

.upload-dropzone.is-dragover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.upload-text {
  margin-bottom: var(--spacing-xs);
}

.upload-link {
  background: none;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
}

.upload-subtext {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}

.upload-error {
  color: var(--color-error);
  margin-top: var(--spacing-md);
}

.uploading {
  margin-top: var(--spacing-md);
  color: var(--color-text-secondary);
}

.preview {
  margin-top: var(--spacing-xl);
}

.preview-table-wrapper {
  overflow: auto;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
}

.preview-table th,
.preview-table td {
  border-bottom: 1px solid var(--color-border);
  padding: var(--spacing-sm);
  text-align: left;
  white-space: nowrap;
}

.drop-columns {
  margin-top: var(--spacing-lg);
}

.drop-columns__title {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
}

.drop-columns__list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.drop-columns__item {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: var(--color-surface);
}

/* User context field CSS */
.dataset-user-context {
  margin-top: var(--spacing-lg);
}

.dataset-user-context-field-description {
  margin-top: -0.7rem;
  margin-bottom: var(--spacing-md);
}

.dataset-user-context__textarea {
  width: 100%;
  min-height: 60px;
  max-height: 200px;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-surface);
  resize: none;
  overflow-y: auto;
  transition: all var(--transition-fast);
  transition-delay: 100ms; /* Technically not needed but makes the animation feel slightly more natural when pressing save. */
  box-sizing: border-box;
}

.dataset-user-context__textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
  min-height: 120px;
}

.dataset-user-context__textarea:hover {
  border-color: var(--color-border-dark);
}

.dataset-user-context__textarea::placeholder {
  color: var(--color-text-muted);
}

.dataset-user-context__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-sm);
}

.dataset-user-context__counter {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  align-self: flex-start;
}

.btn-disabled {
  background-color: var(--color-gray-200);
  color: var(--color-text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-disabled:hover {
  background-color: var(--color-gray-200);
  transform: none;
}
