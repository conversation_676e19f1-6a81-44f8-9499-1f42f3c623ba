/* Target Selection Container Styles */

.target-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-3xl);
  text-align: center;
}

.target-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.target-loading p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.target-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-3xl);
  text-align: center;
}

.target-error p {
  margin: 0;
  color: var(--color-error);
  font-size: var(--font-size-sm);
}

.target-selection-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.target-instruction {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
}

.target-instruction p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
}

/* Target Columns Section */
.target-columns-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.target-section__title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.target-columns-scroll {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
  padding: var(--spacing-xs);
}

.target-column-card {
  background: var(--color-surface);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.target-column-card:hover {
  border-color: var(--color-primary-light);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.target-column-card--selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
  box-shadow: var(--shadow-lg);
}

.target-column-card--selected:hover {
  border-color: var(--color-primary-dark);
}

.target-column-card__name {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.target-column-card--selected .target-column-card__name {
  color: var(--color-primary-dark);
}

.target-column-card__description {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: 1.5;
}

.target-column-card--selected .target-column-card__description {
  color: var(--color-text-primary);
}

/* Target Correlations Section */
.target-correlations-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.target-correlations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.target-correlation-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.target-correlation-card__chart {
  position: relative;
  width: 80px;
  height: 80px;
}

.target-correlation-card__value {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.target-correlation-card__labels {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  text-align: center;
}

.target-correlation-card__feature {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.target-correlation-card__target {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-dark);
}

.target-correlation-card__vs {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  text-transform: uppercase;
}

/* Target Summary */
.target-summary {
  padding: var(--spacing-lg);
  background: var(--color-success-light);
  border-radius: var(--radius-lg);
  text-align: center;
  border: 1px solid var(--color-success);
}

.target-summary p {
  margin: 0;
  color: var(--color-success-dark);
  font-size: var(--font-size-sm);
}

.target-summary strong {
  font-weight: var(--font-weight-bold);
}

/* Responsive Design */
@media (max-width: 768px) {
  .target-columns-scroll {
    max-height: 300px;
  }
  
  .target-column-card {
    padding: var(--spacing-md);
  }
  
  .target-correlations-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: var(--spacing-md);
  }
  
  .target-correlation-card {
    padding: var(--spacing-md);
  }
  
  .target-correlation-card__chart {
    width: 60px;
    height: 60px;
  }
}

/* Scrollbar Styling */
.target-columns-scroll::-webkit-scrollbar {
  width: 6px;
}

.target-columns-scroll::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-sm);
}

.target-columns-scroll::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-sm);
}

.target-columns-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-dark);
}

/* Animation for loading spinner */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
