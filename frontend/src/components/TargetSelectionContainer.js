import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import ContainerBox from './ContainerBox';
import './TargetSelectionContainer.css';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const TargetSelectionContainer = ({ 
  analysis, 
  visible, 
  correlationMatrix,
  onTargetSelected 
}) => {
  const [targetData, setTargetData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTarget, setSelectedTarget] = useState(null);
  const [topCorrelations, setTopCorrelations] = useState([]);

  const isReady = selectedTarget && !loading;

  useEffect(() => {
    if (visible && analysis?.datasets?.[0]?.id && !targetData) {
      fetchTargetData();
    }
  }, [visible, analysis?.datasets, targetData]);

  const fetchTargetData = async () => {
    if (!analysis?.datasets?.[0]?.id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const { apiService } = await import('../services/api');
      const response = await apiService.getTargetSelection(analysis.datasets[0].id);
      setTargetData(response.data);
    } catch (err) {
      setError('Failed to load target selection data. Please try again.');
      console.error('Target selection error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleTargetSelect = (targetColumn) => {
    if (selectedTarget?.name === targetColumn.name) {
      // Deselect if clicking the same target
      setSelectedTarget(null);
      setTopCorrelations([]);
      onTargetSelected?.(null);
    } else {
      setSelectedTarget(targetColumn);
      calculateTopCorrelations(targetColumn.name);
      onTargetSelected?.(targetColumn);
    }
  };

  const calculateTopCorrelations = (targetColumnName) => {
    if (!correlationMatrix) {
      setTopCorrelations([]);
      return;
    }

    // Find correlations for the selected target
    const correlations = [];

    // The correlation matrix comes as a dictionary with string keys like "col1,col2"
    for (const [pairKey, correlation] of Object.entries(correlationMatrix)) {
      // Parse the key which is in format "col1,col2"
      const [col1, col2] = pairKey.split(',').map(s => s.trim());

      if (col1 === targetColumnName && col2 !== targetColumnName) {
        correlations.push({
          feature1: col1,
          feature2: col2,
          correlation: correlation,
          correlation_raw: correlation
        });
      } else if (col2 === targetColumnName && col1 !== targetColumnName) {
        correlations.push({
          feature1: col2,
          feature2: col1,
          correlation: correlation,
          correlation_raw: correlation
        });
      }
    }

    // Sort by correlation strength and take top 5
    correlations.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation));
    setTopCorrelations(correlations.slice(0, 5));
  };

  const createCorrelationGaugeData = (correlation) => {
    const absCorr = Math.abs(correlation.correlation_raw);
    const remaining = 1 - absCorr;
    
    // Color based on correlation strength: red (low) to green (high)
    const red = Math.round(255 * (1 - absCorr));
    const green = Math.round(200 * absCorr); // Reduced from 255 to 200 for less bright green
    const color = `rgb(${red}, ${green}, 0)`;
    
    return {
      labels: ['Correlation', 'Remaining'],
      datasets: [{
        data: [absCorr, remaining],
        backgroundColor: [color, 'rgba(229, 231, 235, 0.3)'],
        borderWidth: 0,
        cutout: '70%',
      }]
    };
  };

  const gaugeOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: { enabled: false }
    }
  };

  if (!visible) return null;

  return (
    <ContainerBox
      title="3. Target Selection"
      isReady={isReady}
      readyLabel="Continue"
      onReadyClick={() => {/* parent can handle next container */}}
    >
      {loading && (
        <div className="target-loading">
          <div className="loading-spinner"></div>
          <p>Loading target selection options...</p>
        </div>
      )}

      {error && (
        <div className="target-error">
          <p>{error}</p>
          <button className="btn btn-primary btn-sm" onClick={fetchTargetData}>
            Retry
          </button>
        </div>
      )}

      {targetData && (
        <div className="target-selection-content">
          <div className="target-instruction">
            <p>Select a target variable for your machine learning model:</p>
          </div>

          {/* Target Columns List */}
          <div className="target-columns-section">
            <h3 className="target-section__title">Available Target Variables</h3>
            <div className="target-columns-scroll">
              {targetData.target_columns.map((column, index) => (
                <div 
                  key={index} 
                  className={`target-column-card ${selectedTarget?.name === column.name ? 'target-column-card--selected' : ''}`}
                  onClick={() => handleTargetSelect(column)}
                >
                  <h4 className="target-column-card__name">{column.name}</h4>
                  <p className="target-column-card__description">{column.use_case}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Top Correlations */}
          {selectedTarget && topCorrelations.length > 0 && (
            <div className="target-correlations-section">
              <h3 className="target-section__title">
                Top Correlations with "{selectedTarget.name}"
              </h3>
              <div className="target-correlations-grid">
                {topCorrelations.map((corr, index) => {
                  const gaugeData = createCorrelationGaugeData(corr);
                  return (
                    <div key={index} className="target-correlation-card">
                      <div className="target-correlation-card__chart">
                        <Doughnut data={gaugeData} options={gaugeOptions} />
                        <div className="target-correlation-card__value">
                          {Math.abs(corr.correlation_raw).toFixed(2)}
                        </div>
                      </div>
                      <div className="target-correlation-card__labels">
                        <div className="target-correlation-card__feature">{corr.feature2}</div>
                        <div className="target-correlation-card__vs">with</div>
                        <div className="target-correlation-card__target">{selectedTarget.name}</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Summary */}
          {selectedTarget && (
            <div className="target-summary">
              <p>
                Selected target: <strong>{selectedTarget.name}</strong>
                {topCorrelations.length > 0 && (
                  <span> • Found {topCorrelations.length} correlated features</span>
                )}
              </p>
            </div>
          )}
        </div>
      )}
    </ContainerBox>
  );
};

export default TargetSelectionContainer;
