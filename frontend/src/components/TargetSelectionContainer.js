import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import ContainerBox from './ContainerBox';
import './TargetSelectionContainer.css';
import './DataAnalysisContainer.css'; // Import to reuse correlation styles

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const TargetSelectionContainer = ({ 
  analysis, 
  visible, 
  correlationMatrix,
  onTargetSelected 
}) => {
  const [targetData, setTargetData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedTarget, setSelectedTarget] = useState(null);
  const [topCorrelations, setTopCorrelations] = useState([]);

  const isReady = selectedTarget && !loading;

  useEffect(() => {
    if (visible && analysis?.datasets?.[0]?.id && !targetData) {
      fetchTargetData();
    }
  }, [visible, analysis?.datasets, targetData]);

  // Load persisted target selection when component becomes visible
  useEffect(() => {
    if (visible && analysis?.datasets?.[0]?.id && targetData && !selectedTarget) {
      loadPersistedTargetSelection();
    }
  }, [visible, analysis?.datasets, targetData, selectedTarget]);

  const fetchTargetData = async () => {
    if (!analysis?.datasets?.[0]?.id) return;

    setLoading(true);
    setError(null);

    try {
      const { apiService } = await import('../services/api');
      const response = await apiService.getTargetSelection(analysis.datasets[0].id);
      setTargetData(response.data);
    } catch (err) {
      setError('Failed to load target selection data. Please try again.');
      console.error('Target selection error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadPersistedTargetSelection = async () => {
    if (!analysis?.datasets?.[0]?.id) return;

    try {
      const { apiService } = await import('../services/api');
      const response = await apiService.getTargetSelectionPersist(analysis.datasets[0].id);
      const persistedTarget = response.data?.target_selection;

      if (persistedTarget && targetData) {
        // Find the target in our current data
        const foundTarget = targetData.target_columns.find(col => col.name === persistedTarget.name);
        if (foundTarget) {
          setSelectedTarget(foundTarget);
          calculateTopCorrelations(foundTarget.name);
          onTargetSelected?.(foundTarget);
        }
      }
    } catch (err) {
      console.error('Failed to load persisted target selection:', err);
      // Don't show error to user, just fail silently
    }
  };

  const saveTargetSelection = async (target) => {
    if (!analysis?.datasets?.[0]?.id) return;

    try {
      const { apiService } = await import('../services/api');
      await apiService.setTargetSelectionPersist(analysis.datasets[0].id, target);
    } catch (err) {
      console.error('Failed to save target selection:', err);
      // Don't show error to user, just fail silently
    }
  };

  const handleTargetSelect = (targetColumn) => {
    if (selectedTarget?.name === targetColumn.name) {
      // Deselect if clicking the same target
      setSelectedTarget(null);
      setTopCorrelations([]);
      onTargetSelected?.(null);
      saveTargetSelection(null); // Save deselection
    } else {
      setSelectedTarget(targetColumn);
      calculateTopCorrelations(targetColumn.name);
      onTargetSelected?.(targetColumn);
      saveTargetSelection(targetColumn); // Save selection
    }
  };

  const calculateTopCorrelations = (targetColumnName) => {
    if (!correlationMatrix) {
      setTopCorrelations([]);
      return;
    }

    // Find correlations for the selected target
    const correlations = [];
    const seenPairs = new Set();

    // The correlation matrix comes as a dictionary with string keys like "col1,col2"
    for (const [pairKey, correlation] of Object.entries(correlationMatrix)) {
      // Parse the key which is in format "col1,col2"
      const [col1, col2] = pairKey.split(',').map(s => s.trim());

      // Skip self-correlations
      if (col1 === col2) continue;

      // Only process if one of the columns is our target
      if (col1 === targetColumnName || col2 === targetColumnName) {
        // Determine the other feature (not the target)
        const otherFeature = col1 === targetColumnName ? col2 : col1;

        // Create a normalized pair key to avoid duplicates (always put target first)
        const normalizedPair = `${targetColumnName},${otherFeature}`;

        // Skip if we've already seen this pair
        if (seenPairs.has(normalizedPair)) continue;
        seenPairs.add(normalizedPair);

        correlations.push({
          feature1: targetColumnName,
          feature2: otherFeature,
          correlation: correlation,
          correlation_raw: correlation
        });
      }
    }

    // Sort by correlation strength and take top 5
    correlations.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation));
    setTopCorrelations(correlations.slice(0, 5));
  };

  const createCorrelationGaugeData = (correlation) => {
    const absCorr = Math.abs(correlation.correlation_raw);
    const remaining = 1 - absCorr;
    
    // Color based on correlation strength: red (low) to green (high)
    const red = Math.round(255 * (1 - absCorr));
    const green = Math.round(200 * absCorr); // Reduced from 255 to 200 for less bright green
    const color = `rgb(${red}, ${green}, 0)`;
    
    return {
      labels: ['Correlation', 'Remaining'],
      datasets: [{
        data: [absCorr, remaining],
        backgroundColor: [color, 'rgba(229, 231, 235, 0.3)'],
        borderWidth: 0,
        cutout: '70%',
      }]
    };
  };

  const gaugeOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: { enabled: false }
    }
  };

  if (!visible) return null;

  return (
    <ContainerBox
      title="3. Target Selection"
      isReady={isReady}
      readyLabel="Train Prediction Model"
      onReadyClick={() => {/* parent can handle next container */}}
    >
      {loading && (
        <div className="target-loading">
          <div className="loading-spinner"></div>
          <p>Loading target selection options...</p>
        </div>
      )}

      {error && (
        <div className="target-error">
          <p>{error}</p>
          <button className="btn btn-primary btn-sm" onClick={fetchTargetData}>
            Retry
          </button>
        </div>
      )}

      {targetData && (
        <div className="target-selection-content">

          {/* Target Columns List */}
          <div className="target-columns-section">
            <div className="target-columns-scroll">
              {targetData.target_columns.map((column, index) => (
                <div 
                  key={index} 
                  className={`target-column-card ${selectedTarget?.name === column.name ? 'target-column-card--selected' : ''}`}
                  onClick={() => handleTargetSelect(column)}
                >
                  <h4 className="target-column-card__name">{column.name}</h4>
                  <p className="target-column-card__description">{column.use_case}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Top Correlations - Reusing DataAnalysisContainer structure */}
          {selectedTarget && topCorrelations.length > 0 && (
            <div className="analysis-section">
              <h3 className="analysis-section__title">
                Top Correlations with "{selectedTarget.name}"
              </h3>
              <div className="correlations-grid">
                {topCorrelations.map((corr, index) => {
                  const gaugeData = createCorrelationGaugeData(corr);
                  return (
                    <div key={index} className="correlation-card">
                      <div className="correlation-card__chart">
                        <Doughnut data={gaugeData} options={gaugeOptions} />
                        <div className="correlation-card__value">
                          {Math.abs(corr.correlation_raw).toFixed(2)}
                        </div>
                      </div>
                      <div className="correlation-card__labels">
                        <div className="correlation-card__feature">{corr.feature2}</div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Conditional note for low correlations */}
              {(() => {
                const avgCorrelation = topCorrelations.reduce((sum, corr) => sum + Math.abs(corr.correlation_raw), 0) / topCorrelations.length;
                return avgCorrelation >= 0.1 && avgCorrelation <= 0.5 ? (
                  <div className="correlation-note">
                    <p>
                      Note that only because the correlation between the scores between single columns and the selected target is rather low, training an accurate model could still be possible. This is due to potentially more complex relationships in the data. Proceed to the next section to find out!
                    </p>
                  </div>
                ) : null;
              })()}
            </div>
          )}
        </div>
      )}
    </ContainerBox>
  );
};

export default TargetSelectionContainer;
