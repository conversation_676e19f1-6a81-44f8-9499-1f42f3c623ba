.analysis-page {
  min-height: 100vh;
  padding: var(--spacing-xl) 0;
  padding-bottom: 100px;
}

.analysis-page__header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
}

.analysis-page__back-btn {
  flex-shrink: 0;
}

.analysis-page__title-section {
  flex-grow: 1;
}

.analysis-page__title-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.analysis-page__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0;
}

.analysis-page__edit-btn {
  background: none;
  border: none;
  color: var(--color-text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  opacity: 0;
}

.analysis-page__title-display:hover .analysis-page__edit-btn {
  opacity: 1;
}

.analysis-page__edit-btn:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.analysis-page__edit-form {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.analysis-page__title-input {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  border: 2px solid var(--color-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-surface);
  min-width: 300px;
  flex-grow: 1;
}

.analysis-page__title-input:focus {
  outline: none;
  border-color: var(--color-primary-hover);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.analysis-page__edit-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.analysis-page__content {
  margin-top: var(--spacing-2xl);
}

.analysis-page__placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.placeholder-content {
  text-align: center;
  max-width: 500px;
  padding: var(--spacing-2xl);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.placeholder-content__icon {
  color: var(--color-text-muted);
  margin-bottom: var(--spacing-lg);
}

.placeholder-content__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.placeholder-content__description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  margin: 0;
}

/* Loading and Error States */
.analysis-page__loading,
.analysis-page__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.analysis-page__loading p,
.analysis-page__error p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-lg);
}

.analysis-page__error h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.analysis-page__error-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analysis-page {
    padding: var(--spacing-lg) 0;
  }

  .analysis-page__header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .analysis-page__title {
    font-size: var(--font-size-xl);
  }

  .analysis-page__title-input {
    font-size: var(--font-size-xl);
    min-width: 250px;
  }

  .analysis-page__edit-form {
    width: 100%;
  }

  .analysis-page__edit-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .placeholder-content {
    padding: var(--spacing-xl);
  }
}

@media (max-width: 480px) {
  .analysis-page__title-input {
    min-width: 200px;
  }

  .analysis-page__error-actions {
    flex-direction: column;
    width: 100%;
  }

  .analysis-page__error-actions .btn {
    width: 100%;
  }
}