import React, { useState, useEffect } from 'react';
import AnalysisCard from '../components/AnalysisCard';
import { apiService } from '../services/api';
import './StartPage.css';

const StartPage = () => {
  const [analyses, setAnalyses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await apiService.getDashboard();
      setAnalyses(response.data.analyses);
      setUser(response.data.user);
      setError(null);
    } catch (err) {
      setError('Failed to load analyses. Please try again.');
      console.error('Error fetching dashboard data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="start-page">
        <div className="container">
          <div className="start-page__loading">
            <div className="loading-spinner"></div>
            <p>Loading your analyses...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="start-page">
        <div className="container">
          <div className="start-page__error">
            <h2>Something went wrong</h2>
            <p>{error}</p>
            <button className="btn btn-primary" onClick={fetchDashboardData}>
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="start-page">
      <div className="container">
        <header className="start-page__header">
          <div className="start-page__welcome">
            <h1>Welcome back{user?.full_name ? `, ${user.full_name}` : ''}!</h1>
            <p className="start-page__subtitle">
              {analyses.length === 0
                ? "Ready to start your first analysis?"
                : `You have ${analyses.length} analysis${analyses.length !== 1 ? 'es' : ''}`
              }
            </p>
          </div>
        </header>

        <main className="start-page__content">
          <div className="start-page__section">
            <h2 className="start-page__section-title">Your Analyses</h2>

            <div className="analyses-grid grid grid-cols-1 grid-cols-2 grid-cols-3">
              {/* New Analysis Card - Always first */}
              <AnalysisCard isNewCard={true} />

              {/* Existing Analyses */}
              {analyses.map((analysis) => (
                <AnalysisCard
                  key={analysis.id}
                  analysis={analysis}
                />
              ))}
            </div>

            {analyses.length === 0 && (
              <div className="start-page__empty-state">
                <div className="empty-state">
                  <div className="empty-state__icon">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                      <path d="M9 19c-5 0-8-3-8-8s3-8 8-8 8 3 8 8-3 8-8 8z"></path>
                      <path d="M21 21l-4.35-4.35"></path>
                    </svg>
                  </div>
                  <h3 className="empty-state__title">No analyses yet</h3>
                  <p className="empty-state__description">
                    Create your first analysis to get started with data exploration and insights.
                  </p>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default StartPage;