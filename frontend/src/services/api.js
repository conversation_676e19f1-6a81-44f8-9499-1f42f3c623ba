import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// API functions
export const apiService = {
  // Dashboard
  getDashboard: () => api.get('/dashboard/'),

  // Analyses
  getAnalyses: () => api.get('/analyses/'),
  getAnalysis: (id) => api.get(`/analyses/${id}/`),
  createAnalysis: (data) => api.post('/analyses/', data),
  updateAnalysis: (id, data) => api.patch(`/analyses/${id}/`, data),
  deleteAnalysis: (id) => api.delete(`/analyses/${id}/`),

  // Datasets
  getDatasets: (analysisId) => api.get(`/analyses/${analysisId}/datasets/`),
  getDataset: (id) => api.get(`/datasets/${id}/`),
  getDatasetPreview: (id) => api.get(`/datasets/${id}/preview/`),
  createDataset: (analysisId, data) => api.post(`/analyses/${analysisId}/datasets/`, data),
  uploadDataset: (analysisId, formData) => api.post(`/analyses/${analysisId}/datasets/`, formData, { headers: { 'Content-Type': 'multipart/form-data' } }),
  updateDataset: (id, data) => api.patch(`/datasets/${id}/`, data),
  deleteDataset: (id) => api.delete(`/datasets/${id}/`),
  getDropColumns: (id) => api.get(`/datasets/${id}/drop-columns/`),
  setDropColumns: (id, dropColumns) => api.patch(`/datasets/${id}/drop-columns/`, { drop_columns: dropColumns }),
  getUserContext: (id) => api.get(`/datasets/${id}/dataset-user-context/`),
  setUserContext: (id, userContext) => api.patch(`/datasets/${id}/dataset-user-context/`, { user_context: userContext }),
  analyzeDataset: (id) => api.post(`/datasets/${id}/analyze/`),
};

export default api;