/* Color System for Tableyze */
:root {
  /* Primary Colors */
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-primary-light: #dbeafe;
  --color-primary-dark: #1d4ed8;

  /* Secondary Colors */
  --color-secondary: #8b5cf6;
  --color-secondary-hover: #7c3aed;
  --color-secondary-light: #ede9fe;
  --color-secondary-dark: #6d28d9;

  /* Background Colors */
  --color-background: #ffffff;
  --color-background-secondary: #f8fafc;
  --color-background-tertiary: #f1f5f9;

  /* Surface/Card Colors */
  --color-surface: #ffffff;
  --color-surface-hover: #f8fafc;
  --color-surface-secondary: #f1f5f9;
  --color-surface-tertiary: #e2e8f0;

  /* Border/Divider Colors */
  --color-border: #e2e8f0;
  --color-border-light: #f1f5f9;
  --color-border-dark: #cbd5e1;
  --color-divider: #e2e8f0;

  /* Text Colors */
  --color-text-primary: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #ffffff;
  --color-text-muted: #94a3b8;

  /* Functional Colors - Success */
  --color-success: #10b981;
  --color-success-hover: #059669;
  --color-success-light: #d1fae5;
  --color-success-dark: #047857;

  /* Functional Colors - Warning */
  --color-warning: #f59e0b;
  --color-warning-hover: #d97706;
  --color-warning-light: #fef3c7;
  --color-warning-dark: #b45309;

  /* Functional Colors - Error */
  --color-error: #ef4444;
  --color-error-hover: #dc2626;
  --color-error-light: #fee2e2;
  --color-error-dark: #b91c1c;

  /* Functional Colors - Info */
  --color-info: #3b82f6;
  --color-info-hover: #2563eb;
  --color-info-light: #dbeafe;
  --color-info-dark: #1d4ed8;

  /* Neutral Grays */
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* Spacing */
  --spacing-xxs: 0.05rem;
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.4rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-content: 4rem 2rem 2rem 2rem;

  /* Typography */
  --font-size-xxs: 0.65rem;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}