{"name": "tableyze", "version": "1.0.0", "description": "Revolutionizing how AI makes the most of tabular data", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "backend:dev": "cd backend && source venv/bin/activate && python manage.py runserver", "frontend:dev": "cd frontend && npm start", "backend:migrate": "cd backend && source venv/bin/activate && python manage.py migrate", "backend:makemigrations": "cd backend && source venv/bin/activate && python manage.py makemigrations", "backend:shell": "cd backend && source venv/bin/activate && python manage.py shell", "backend:createsuperuser": "cd backend && source venv/bin/activate && python manage.py createsuperuser", "backend:collectstatic": "cd backend && source venv/bin/activate && python manage.py collectstatic --noinput", "frontend:build": "cd frontend && npm run build", "frontend:test": "cd frontend && npm test", "install:backend": "cd backend && source venv/bin/activate && pip install -r requirements.txt", "install:frontend": "cd frontend && npm install", "install:all": "npm run install:backend && npm run install:frontend", "setup": "npm run install:all && npm run backend:migrate"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["django", "react", "tabular-data", "ai", "data-analysis"], "author": "", "license": "MIT"}